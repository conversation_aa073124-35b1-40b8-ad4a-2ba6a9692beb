"""
Memory Handler Functions for MCP Tools

Extracted helper functions to reduce complexity in main MCP tool handlers.
Follows single responsibility principle for improved maintainability.
"""

import time
import json
import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple

from config import get_config
from exceptions import (
    MCPProtocolError, MemoryExtractionError, EmbeddingGenerationError,
    DatabaseQueryError, create_error_response, SparkMemoryError
)

logger = logging.getLogger(__name__)
config = get_config()


async def build_extraction_context(memory_system, user_id: str, conversation_history: Optional[List[Tuple[str, str]]] = None) -> Dict[str, Any]:
    """
    Build enhanced context for memory extraction with performance tracking and conversation awareness.
    
    Args:
        memory_system: The memory system context
        user_id: User identifier
        conversation_history: Optional list of (user_msg, assistant_msg) tuples for context
        
    Returns:
        Enhanced context dictionary for extraction with metadata tracking
        
    Raises:
        DatabaseQueryError: If context building fails
    """
    try:
        context_start = time.time()
        
        # Get basic context
        summary_start = time.time()
        conversation_summary = await memory_system.summary.get_conversation_summary(user_id)
        summary_time = (time.time() - summary_start) * 1000
        logger.info(f"⏱️ Conversation summary took {summary_time:.0f}ms")
        
        db_start = time.time()
        recent_memories = await memory_system.db.get_recent_memories(
            user_id, 
            limit=config.database.RECENT_MEMORIES_LIMIT
        )
        db_time = (time.time() - db_start) * 1000
        logger.info(f"⏱️ Recent memories query took {db_time:.0f}ms")
        
        # Build enhanced context with metadata
        context = {
            "conversation_summary": conversation_summary,
            "recent_messages": recent_memories,
            "user_id": user_id,
            # Enhanced metadata tracking
            "metadata": {
                "extraction_timestamp": time.time(),
                "conversation_length": len(conversation_history) if conversation_history else 0,
                "has_summary": bool(conversation_summary),
                "recent_memory_count": len(recent_memories),
                "session_context": {
                    "start_time": context_start,
                    "extraction_phase": "context_building"
                }
            },
            # Conversation awareness
            "conversation_history": conversation_history[-5:] if conversation_history else [],  # Last 5 exchanges
            "conversation_topics": _extract_conversation_topics(conversation_history) if conversation_history else [],
            # Temporal context
            "temporal_markers": {
                "current_time": time.time(),
                "session_duration": 0,  # To be updated
                "memory_freshness": _calculate_memory_freshness(recent_memories)
            }
        }
        
        # Add graph context if available
        if hasattr(memory_system, 'db') and memory_system.db.is_graph_enabled():
            context["graph_enabled"] = True
            context["metadata"]["graph_integration"] = "active"
        
        context_time = (time.time() - context_start) * 1000
        context["metadata"]["context_build_time_ms"] = context_time
        logger.info(f"Enhanced context building took {context_time:.0f}ms with {len(recent_memories)} recent memories")
        
        return context
        
    except Exception as e:
        raise DatabaseQueryError(
            f"Failed to build extraction context: {str(e)}",
            user_id=user_id
        )


def _extract_conversation_topics(conversation_history: Optional[List[Tuple[str, str]]]) -> List[str]:
    """Extract main topics from conversation history."""
    if not conversation_history:
        return []
    
    topics = []
    # Simple keyword extraction - could be enhanced with NLP
    keywords = ["project", "technology", "skill", "person", "team", "goal", "preference", "update", "change"]
    
    for user_msg, assistant_msg in conversation_history[-3:]:  # Last 3 exchanges
        combined = f"{user_msg} {assistant_msg}".lower()
        for keyword in keywords:
            if keyword in combined and keyword not in topics:
                topics.append(keyword)
    
    return topics[:5]  # Limit to top 5 topics


def _calculate_memory_freshness(recent_memories: List[str]) -> Dict[str, Any]:
    """Calculate freshness metrics for recent memories."""
    if not recent_memories:
        return {"has_recent": False, "count": 0}
    
    return {
        "has_recent": True,
        "count": len(recent_memories),
        "diversity": len(set(recent_memories)) / len(recent_memories) if recent_memories else 0
    }


async def extract_memory_candidates(memory_system, text: str, context: Dict[str, Any]) -> Tuple[List[str], Dict[str, Any]]:
    """
    Extract memory candidates from text using enhanced Phase 1 pipeline with metadata.
    
    Args:
        memory_system: The memory system context
        text: Input text to extract memories from
        context: Enhanced extraction context with metadata
        
    Returns:
        Tuple of (extracted memory candidates, extraction metadata)
        
    Raises:
        MemoryExtractionError: If extraction fails
    """
    try:
        # Create message pair with conversation awareness
        conversation_history = context.get("conversation_history", [])
        if conversation_history:
            # Use last user message as context if available
            prev_msg = conversation_history[-1][0] if conversation_history else ""
            message_pair = (prev_msg, text)
        else:
            message_pair = ("", text)  # Empty previous message for single input
        
        # Prepare enhanced metadata for extraction
        enhanced_metadata = {
            "conversation_topics": context.get("conversation_topics", []),
            "temporal_context": context.get("temporal_markers", {}),
            "graph_enabled": context.get("graph_enabled", False),
            "extraction_context": "conversation_aware",
            "session_metadata": context.get("metadata", {})
        }
        
        # Phase 1: Enhanced extraction with performance tracking
        extraction_start = time.time()
        
        # Use enhanced extraction method if available
        if hasattr(memory_system.extraction, 'extract_with_enhanced_metadata'):
            candidates, extraction_metadata = await memory_system.extraction.extract_with_enhanced_metadata(
                message_pair, context, enhanced_metadata
            )
        else:
            # Fallback to standard extraction
            candidates = await memory_system.extraction.extract_memories(message_pair, context)
            extraction_metadata = {"method": "standard", "enhanced": False}
        
        extraction_time = (time.time() - extraction_start) * 1000
        
        # Update extraction metadata
        extraction_metadata.update({
            "extraction_time_ms": extraction_time,
            "candidate_count": len(candidates),
            "context_used": bool(conversation_history),
            "topics_identified": len(context.get("conversation_topics", [])),
            "evolution_tracking": enhanced_metadata.get("graph_enabled", False)
        })
        
        logger.info(f"Enhanced Phase 1 extraction: {extraction_time:.0f}ms, {len(candidates)} candidates, topics: {extraction_metadata.get('topics_identified', 0)}")
        
        return candidates, extraction_metadata
        
    except Exception as e:
        raise MemoryExtractionError(
            f"Enhanced memory extraction failed: {str(e)}",
            llm_response=None,
            parsing_method="enhanced_two_phase_pipeline"
        )


async def generate_batch_embeddings(memory_system, candidates: List[str]) -> Optional[List[List[float]]]:
    """
    Generate embeddings for all candidates using batch processing.
    
    Args:
        memory_system: The memory system context
        candidates: List of candidate memory strings
        
    Returns:
        List of embedding vectors, or None if batch generation fails
        
    Raises:
        EmbeddingGenerationError: If embedding generation fails critically
    """
    if not candidates:
        return []
    
    embedding_start = time.time()
    
    try:
        candidate_embeddings = await memory_system.bge.embed_texts(
            candidates, add_instruction=False
        )
        embedding_time = (time.time() - embedding_start) * 1000
        
        logger.info(f"BGE embedding generation: {embedding_time:.0f}ms, {len(candidate_embeddings)} embeddings")
        
        return candidate_embeddings
        
    except Exception as e:
        embedding_time = (time.time() - embedding_start) * 1000
        logger.error(f"Batch embedding failed after {embedding_time:.0f}ms, falling back to individual: {e}")
        
        # Return None to indicate fallback should be used
        return None


async def process_candidates_with_embeddings(
    memory_system,
    candidates: List[str],
    embeddings: List[List[float]],
    user_id: str,
    session_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Process candidates using pre-computed embeddings.

    Args:
        memory_system: The memory system context
        candidates: List of candidate memory strings
        embeddings: Pre-computed embedding vectors
        user_id: User identifier
        session_id: Optional session identifier for session-based tracking

    Returns:
        List of processing results
    """
    candidate_tasks = [
        memory_system.update.process_candidate_memory_with_embedding(
            candidate, embedding, user_id, session_id
        )
        for candidate, embedding in zip(candidates, embeddings)
    ]
    
    return await execute_candidate_processing(candidate_tasks, len(candidates))


async def process_candidates_individually(
    memory_system,
    candidates: List[str],
    user_id: str,
    session_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Process candidates individually (fallback method).

    Args:
        memory_system: The memory system context
        candidates: List of candidate memory strings
        user_id: User identifier
        session_id: Optional session identifier for session-based tracking

    Returns:
        List of processing results
    """
    candidate_tasks = [
        memory_system.update.process_candidate_memory(candidate, user_id, session_id)
        for candidate in candidates
    ]
    
    return await execute_candidate_processing(candidate_tasks, len(candidates))


async def execute_candidate_processing(
    candidate_tasks: List, 
    candidate_count: int
) -> List[Dict[str, Any]]:
    """
    Execute candidate processing tasks with timeout and error handling.
    
    Args:
        candidate_tasks: List of async tasks to execute
        candidate_count: Number of candidates being processed
        
    Returns:
        List of processing results
    """
    try:
        # Execute in parallel with timeout
        results = await asyncio.wait_for(
            asyncio.gather(*candidate_tasks, return_exceptions=True), 
            timeout=config.performance.OPERATION_TIMEOUT
        )
    except asyncio.TimeoutError:
        logger.error(f"Candidate processing timed out after {config.performance.OPERATION_TIMEOUT} seconds")
        return [{"operation": "TIMEOUT", "reasoning": "Processing timed out"}] * candidate_count
    
    # Handle exceptions in parallel processing
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Error processing candidate {i}: {result}")
            processed_results.append({"operation": "ERROR", "reasoning": str(result)})
        else:
            processed_results.append(result)
    
    return processed_results


async def process_memory_pipeline(memory_system, text: str, user_id: str, conversation_history: Optional[List[Tuple[str, str]]] = None, session_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Execute the complete enhanced two-phase memory pipeline with metadata tracking.

    Args:
        memory_system: The memory system context
        text: Input text to process
        user_id: User identifier
        conversation_history: Optional conversation history for context awareness
        session_id: Optional session identifier for session-based tracking

    Returns:
        Dictionary with enhanced processing results and metadata

    Raises:
        SparkMemoryError: If any phase of processing fails
    """
    pipeline_start = time.time()
    
    # Build enhanced context for extraction
    context = await build_extraction_context(memory_system, user_id, conversation_history)
    
    # Phase 1: Extract memory candidates with metadata
    candidates, extraction_metadata = await extract_memory_candidates(memory_system, text, context)
    
    # Phase 2: Process candidates if any were extracted
    results = []
    processing_metadata = {"method": "none", "batch_embeddings": False}
    
    if candidates:
        logger.debug(f"Processing {len(candidates)} candidates in parallel with metadata tracking")
        
        # Try batch embedding generation first
        candidate_embeddings = await generate_batch_embeddings(memory_system, candidates)
        
        if candidate_embeddings and len(candidate_embeddings) == len(candidates):
            # Use pre-computed embeddings for faster processing
            results = await process_candidates_with_embeddings(
                memory_system, candidates, candidate_embeddings, user_id, session_id
            )
            processing_metadata = {"method": "batch", "batch_embeddings": True}
        else:
            # Fallback to individual processing
            results = await process_candidates_individually(
                memory_system, candidates, user_id, session_id
            )
            processing_metadata = {"method": "individual", "batch_embeddings": False}
    
    # Prepare enhanced summary update with metadata
    summary_metadata = {
        "topics": context.get("conversation_topics", []),
        "extraction_metadata": extraction_metadata,
        "processing_metadata": processing_metadata,
        "graph_enabled": context.get("graph_enabled", False)
    }
    
    # Update rolling summary asynchronously with enhanced metadata
    asyncio.create_task(
        _update_summary_with_metadata(memory_system, user_id, [text], summary_metadata)
    )
    
    # Calculate operation statistics
    operation_stats = _calculate_operation_stats(results)
    
    pipeline_time = (time.time() - pipeline_start) * 1000
    
    return {
        "success": True,
        "candidates_processed": len(candidates),
        "operations": [r["operation"] for r in results],
        "operation_stats": operation_stats,
        "user_id": user_id,
        "metadata": {
            "pipeline_time_ms": pipeline_time,
            "extraction": extraction_metadata,
            "processing": processing_metadata,
            "context": {
                "conversation_aware": bool(conversation_history),
                "topics_identified": len(context.get("conversation_topics", [])),
                "graph_integration": context.get("graph_enabled", False)
            }
        }
    }


async def _update_summary_with_metadata(memory_system, user_id: str, texts: List[str], metadata: Dict[str, Any]):
    """Update rolling summary with enhanced metadata tracking."""
    try:
        # Check if summary module supports enhanced updates
        if hasattr(memory_system.summary, 'update_summary_with_metadata'):
            await memory_system.summary.update_summary_with_metadata(user_id, texts, metadata)
        else:
            # Fallback to standard update
            await memory_system.summary.update_summary_async(user_id, texts)
    except Exception as e:
        logger.error(f"Failed to update summary with metadata: {e}")


def _calculate_operation_stats(results: List[Dict[str, Any]]) -> Dict[str, int]:
    """Calculate statistics on memory operations performed."""
    stats = {"ADD": 0, "UPDATE": 0, "DELETE": 0, "NOOP": 0, "ERROR": 0, "TIMEOUT": 0}
    
    for result in results:
        operation = result.get("operation", "ERROR")
        if operation in stats:
            stats[operation] += 1
        else:
            stats["ERROR"] += 1
    
    return stats


def create_context_access_error_response(context_error: Exception, user_id: str, function_name: str) -> Dict[str, Any]:
    """
    Create standardized error response for context access failures.
    
    Args:
        context_error: The context access exception
        user_id: User identifier
        function_name: Name of the function where error occurred
        
    Returns:
        Standardized error response dictionary
    """
    return {
        "success": False,
        "error": "context_access_error", 
        "message": f"Context access failed: {str(context_error)}",
        "function": function_name,
        "user_id": user_id,
        "candidates_processed": 0,
        "operations": []
    }


def create_memory_operation_error_response(
    exception: Exception, 
    user_id: str, 
    function_name: str
) -> Dict[str, Any]:
    """
    Create standardized error response for memory operations.
    
    Args:
        exception: The exception that occurred
        user_id: User identifier  
        function_name: Name of the function where error occurred
        
    Returns:
        Standardized error response dictionary
    """
    if isinstance(exception, SparkMemoryError):
        error_response = create_error_response(exception, function_name)
        error_response["user_id"] = user_id
        return error_response
    
    # Handle unknown exceptions
    from exceptions import handle_known_exception
    spark_exception = handle_known_exception(exception)
    error_response = create_error_response(spark_exception, function_name)
    error_response["user_id"] = user_id
    
    return error_response


async def get_memory_system_context(ctx, function_name: str):
    """
    Safely access memory system context with proper error handling.
    
    Args:
        ctx: MCP context
        function_name: Name of the calling function
        
    Returns:
        Memory system context
        
    Raises:
        MCPProtocolError: If context access fails
    """
    try:
        return ctx.request_context.lifespan_context
    except Exception as context_error:
        logger.error(f"Context access failed in {function_name}: {context_error}")
        raise MCPProtocolError(
            f"Failed to access memory system context: {str(context_error)}",
            mcp_tool=function_name,
            context_error=str(context_error)
        )