"""
Uvicorn wrapper for FastMCP to enable hot reload in development.

This wrapper allows the MCP server to work with uvicorn's --reload flag.
"""

import os
import async<PERSON>
import json
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request, Response
from fastapi.responses import StreamingResponse
import logging

# Import the MCP server
from main_new import mcp, logger

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle."""
    # Initialize MCP server
    logger.info("Starting MCP server in development mode")

    # The MCP server should already be initialized via its decorators
    # Just need to ensure it's ready

    yield

    # Cleanup
    logger.info("Shutting down MCP server")

# Create FastAPI app wrapper
app = FastAPI(title="Spark Memory MCP Server", lifespan=lifespan)

@app.get("/")
async def root():
    """Root endpoint for health checks."""
    return {"service": "spark-mcp-server", "status": "ok"}

@app.get("/health")
async def health():
    """Health check endpoint."""
    return {"status": "ok", "service": "spark-mcp-server", "mode": "development"}

@app.post("/mcp")
async def mcp_endpoint(request: Request):
    """Main MCP endpoint for SSE transport."""
    body = await request.body()
    
    async def event_generator():
        """Generate SSE events from MCP."""
        # This is a simplified implementation
        # In production, you'd properly handle the MCP protocol
        yield f"data: {{'type': 'message', 'content': 'MCP endpoint'}}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        }
    )

# For development, list available tools
@app.get("/tools")
async def list_tools():
    """List available MCP tools for development/testing."""
    # This is a simplified list for development
    # In production, tools are accessed via MCP protocol
    return {
        "available_tools": [
            "add_memories",
            "search_memory",
            "search_by_entity",
            "get_memories",
            "delete_memory",
            "delete_all_memories",
            "health_check",
            "get_performance_stats",
            "get_bge_health_status",
            "benchmark_bge_performance"
        ],
        "note": "Use MCP client for actual tool execution"
    }

# Lifespan function moved above FastAPI app creation

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", "8050"))
    uvicorn.run(
        "main_dev:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        reload_dirs=["src"],
        log_level="debug" if os.getenv("ENVIRONMENT") == "development" else "info"
    )
