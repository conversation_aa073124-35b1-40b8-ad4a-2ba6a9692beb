version: '3.8'

services:
  # ==========================================
  # Spark Memory MCP Server - Development Mode
  # ==========================================
  spark-mcp-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: spark-mcp-dev
    ports:
      - "${PORT:-8050}:8050"
    environment:
      # Development mode
      ENVIRONMENT: development
      PYTHONUNBUFFERED: 1

      # MCP Server Configuration
      TRANSPORT: sse
      HOST: ${HOST:-0.0.0.0}
      PORT: ${PORT:-8050}

      # LLM Configuration
      LLM_PROVIDER: ${LLM_PROVIDER:-mock}
      LLM_API_KEY: ${LLM_API_KEY:-mock-key}
      LLM_CHOICE: ${LLM_CHOICE:-mock-model}
      LLM_BASE_URL: ${LLM_BASE_URL:-http://mock}

      # BGE Embedding Server
      BGE_SERVER_URL: ${BGE_SERVER_URL:-http://************:8080}

      # Database Configuration
      DATABASE_URL: ${DATABASE_URL:-******************************************************/spark_memory}

      # Neo4j Configuration
      NEO4J_URI: ${NEO4J_URI:-bolt://host.docker.internal:7687}
      NEO4J_USER: ${NEO4J_USER:-neo4j}
      NEO4J_PASSWORD: ${NEO4J_PASSWORD}

      # Redis Configuration for LLM Caching
      REDIS_URL: ${REDIS_URL:-redis://redis:6379/0}

      # User ID
      MCP_USER_ID: ${MCP_USER_ID:-dev-team}

      # Logging
      UVICORN_LOG_LEVEL: debug
      LOG_LEVEL: DEBUG
    networks:
      - spark-dev-network
    volumes:
      # Hot reload - mount source code
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - ./pyproject.toml:/app/pyproject.toml:ro
      - ./logs:/app/logs
    restart: unless-stopped
    command: ["uv", "run", "--no-dev", "python", "src/main_new.py"]
    depends_on:
      postgres-dev:
        condition: service_healthy
      redis:
        condition: service_healthy

  # ==========================================
  # PostgreSQL with pgvector - Development
  # ==========================================
  postgres-dev:
    image: pgvector/pgvector:pg16
    container_name: spark-postgres-dev
    environment:
      POSTGRES_DB: spark_memory
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: spark_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    networks:
      - spark-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d spark_memory"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      postgres 
      -c shared_preload_libraries=vector
      -c max_connections=200
      -c shared_buffers=256MB
      -c log_statement=all
      -c log_duration=on

  # ==========================================
  # pgAdmin for Database Management (Optional)
  # ==========================================
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: spark-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: "False"
    ports:
      - "5050:80"
    networks:
      - spark-dev-network
    restart: unless-stopped
    profiles:
      - tools
    volumes:
      - pgadmin_data:/var/lib/pgadmin

  # ==========================================
  # Redis for LLM Response Caching
  # ==========================================
  redis:
    image: redis:7-alpine
    container_name: spark-redis
    ports:
      - "6379:6379"
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - spark-dev-network
    restart: unless-stopped



networks:
  spark-dev-network:
    driver: bridge
    name: spark-dev-network

volumes:
  postgres_dev_data:
    name: spark_postgres_dev_data
  pgadmin_data:
    name: spark_pgadmin_data
  redis_data:
    name: spark_redis_data
